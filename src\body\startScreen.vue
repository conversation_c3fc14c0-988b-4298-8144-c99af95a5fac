<template>
    <div class="w-full bg-ordy h-auto z-50">
        <img src="../assets/img/background_homepage.png" class="absolute mt-[700px] z-0" />

        <div class="p-8 mb-24">
            <!-- head -->
            <div class="w-full md:w-9/12 mx-auto flex flex-wrap font-OpenSans uppercase font-bold text-tiny z-10 md:pr-36">
                <div class="w-2/5 md:w-1/6 px-2">
                    <img src="../assets/ordy_logo.svg" alt="Logo" class="ml-0">
                </div>
                <div class="w-3/5 md:flex md:flex-row md:w-5/6 md:gap-5 px-6 mt-5">
                    <div class="w-full md:w-1/2">
                        <div class="w-full h-10 rounded-2xl text-center pt-3 z-10">
                            <button @click="goLogs()" class="w-full text-black h-4 uppercase z-10">News</button>
                        </div>
                    </div>
                    <div class="w-full md:w-1/2">
                        <div class="w-full bg-ordypurple-100 h-10 rounded-2xl text-center pt-3 z-10">
                            <button @click="goLogin()" class="w-full h-4 text-white uppercase z-10">
                                <span v-if="!userStore.user.id">einloggen</span>
                                <span v-if="userStore.user.id">starten</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- head -->

            <!-- first -->
            <div class="md:w-9/12 w-full mx-auto flex md:flex-row flex-col h-auto mt-8 p-3 z-10">
                <div class="md:w-6/12 w-full my-auto flex flex-col mt-20">
                    <h1 class="font-YesevaOne text-center md:text-left md:text-[5em] text-[3.8em] h-auto font-normal 
                    md:leading-[6rem] break-normal pr-2 leading-[3.2rem] w-full float-right z-10">
                        Kochen war noch nie so einfach
                    </h1>
                    <p class="md:w-full mx-auto mt-3 text-center md:text-left font-OpenSans md:pt-12 pt-4 z-10">
                        Kreiere und verwalte deine Rezepte <span class="font-bold">spielerisch, magisch</span> und auf die <span class="font-bold">Bedürfnisse deines Haushalts</span> genau zugeschnitten.
                    </p>
                    <div class="w-full flex md:flex-row flex-col h-auto md:mt-6 md:justify-items-start ">
                        <button @click="scrollToSection()" class="bg-white border-3 px-6 py-3 rounded-xl text-black 
                        uppercase font-semibold shadow-lg hover:bg-gray-25 transition block md:inline-block z-10 mt-12">
                            über die app
                        </button>

                        <button @click="goLogin" class="bg-ordypurple-100 md:ml-8 px-6 py-3 rounded-xl text-white
                        uppercase font-semibold shadow-lg hover:bg-ordypurple-200 transition md:inline-flex z-10 mt-4 md:mt-12 flex items-center justify-center">
                            <span v-if="!userStore.user.id">registrieren</span>
                            <span v-if="userStore.user.id">starten</span>
                        </button>
                    </div>
                </div>

                <!-- Container für Menükarte und Lottie-Animation -->
                <div class="lottie-host-container w-full md:w-4/12 mx-auto md:p-0 p-6 mt-16 md:mt-0 md:ml-24 z-10 flex flex-row relative">
                    <!-- Lottie Animation als Hintergrund für diesen gesamten Container -->
                    <div class="lottie-animation-container absolute top-0 left-0 w-full h-full z-0">
                        <Vue3Lottie
                            v-if="animationDataLoaded"
                            :animationData="lottieAnimation" 
                            :loop="true"
                            :autoplay="true"
                            :width="'100%'"
                            :height="'100%'"
                            class="lottie-player-instance-bg"
                        />
                    </div>

                    <!-- Oranger rotierender Schatten hinter der Menükarte -->
                    <div class="menu-card-orange-shadow pointer-events-none">
                      <div class="menu-card-orange-shadow-fleck"></div>
                    </div>

                    <!-- Bestehende MenuCard -->
                    <div class="menu-card-copy relative overflow-hidden rounded-xl bg-cover bg-center w-full flex flex-col justify-between antialiased z-1" 
                         :style="{ 
                             backgroundImage: `url(${bernerRoestiImg})`, 
                             aspectRatio: '2/3', 
                             height: 'auto'
                         }"
                         :class="{ 'card-animations-active': startCardAnimations }">
                        <!-- Inhalt direkt hier -->
                        <!-- head -->
                        <div class="w-full mt-2 p-4 md:p-6 flex flex-row text-right text-white">
                            <div class="w-4/5 pr-2 md:pr-4">
                                <h3 class="title-element mt-1 text-[1.8em] sm:text-4xl md:text-3xl lg:text-2xl xl:text-3xl 2xl:text-3xl 3xl:text-[2.5em] relative font-semibold" style="text-shadow: 1px 1px 3px rgba(0,0,0,0.7);">{{ displayedTitle }}</h3>
                            </div>
                            <div class="w-1/5">
                                <button class="mt-4 xl:mt-2 2xl:mt-4 w-14 h-14 2xl:w-18 2xl:h-18 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-150 reload-icon-container">
                                    <img :src="reloadIcon" class="mx-auto h-9 w-9 2xl:w-12 2xl:h-12 md:h-6 md:w-6 reload-icon-img" alt="reload" />
                                </button>
                            </div>
                        </div>
                        <!-- foot -->
                        <div class="absolute bottom-0 left-0 right-0 p-4 md:p-6 flex flex-nowrap gap-2 md:gap-4">
                            <div class="footer-button-1 w-5/12 opacity-0">
                                <div class="h-9 w-full bg-white bg-opacity-90 rounded-lg flex flex-row justify-center items-center px-1 shadow-sm border border-gray-200 text-black">
                                    <span class="text-xs md:text-sm">4</span>
                                    <img class="h-5 md:h-6 mx-1" :src="peopleIcon" alt="people" />
                                    <span class="break-keep text-xs md:text-sm">25 min</span>
                                </div>
                            </div>
                            <div class="footer-button-2 w-5/12 opacity-0">
                                <button class="h-9 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-lg w-full text-xs md:text-sm shadow-sm border border-gray-200 text-ordypurple-700 cursor-pointer transition-all duration-150">
                                    <span class="button-typing-text">zum Rezept</span>
                                </button>
                            </div>
                            <div class="footer-button-3 w-2/12 mx-auto h-auto opacity-0">
                                <button class="h-9 w-9 float-right rounded-full transition-all duration-150 focus:outline-none bg-white bg-opacity-90 hover:bg-opacity-100 shadow-sm border border-gray-200">
                                    <img :src="addIcon" class="mx-auto h-5 w-5" alt="add" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- first -->

            
            <!-- second -->
            <div class="w-full md:w-5/12 mx-auto flex flex-col h-auto mt-24 z-10">
                <span class="font-bold text-ordypurple-100 text-center z-10">FÜR FREUNDE UND FAMILIE</span>
                <h2 class="font-YesevaOne text-[3em] h-auto font-normal md:leading-[6rem] leading-[3rem] pt-4 pb-4 md:mb-0 md:pt-0 w-full text-center z-10">Wir bieten</h2>
                <p class="text-center z-10 w-full">Ordy ist einfacher und praktischer Menüplaner. Planen und verwalten Sie Ihre Menü-Ideen nach Herzenslust. Die Unterstützung beim Kochen wird Sie umhauen!</p>
            </div>
            <!-- second -->


            <!-- second -->
            <div class="md:w-9/12 w-full p-3 mx-auto flex flex-col h-auto mt-8 md:pl-12 z-10" id="overtheapp">
                <carouselBanner></carouselBanner>
            </div>
            <!-- second -->


            <!-- second -->
            <div class="w-9/12 mx-auto flex flex-col h-auto mt-32 md:pl-12 z-10">
                <div class="md:w-7/12 w-full my-auto flex flex-col z-10">
                    <span class="font-bold w-full text-ordypurple-200 text-center md:text-left z-10">ZUSAMMEN ERREICHT MAN MEHR</span>
                    <h2 class="font-YesevaOne text-[3em] h-auto font-normal leading-[6rem] w-full text-left z-10">Funktionen</h2>
                </div>
                <div class="flex md:flex-row flex-col md:w-[140%] gap-12">
                    <!-- element -->
                    <div class="w-[278px] h-auto bg-white rounded-[25px] shadow-md z-10">
                        <div class="w-4/5 h-auto mx-auto p-5 mt-9">
                            <div class="flex md:flex-row flex-col w-4/5 mx-auto">
                                <img class="w-2/8 md:w-2/8 pr-2 z-10 mx-auto" src="../assets/icons/wochenplan.png" />
                                <div class="w-6/8 font-bold mx-auto text-center font-OpenSans z-10 uppercase">planen</div>
                            </div>
                            <p class="text-tiny mt-5 z-10 text-center md:text-left">
                                Unser Menüplaner unterstützt sie bei einer gesunden und <span class="font-bold">ausgewogener Ernährung</span> nach ihren Bedürfnissen. Für Menüs die öfter im Wochenplan auftauchen, finden wir auch mal <span class="font-bold">abwechslungsreiche Alternativen</span>. So erleben Sie sicher einen kulinarischen Höhenflug - im Alltag - einfach so nebenbei
                            </p>
                            <br />
                        </div>
                    </div>
                    <!-- element -->
                    <div class="w-[278px] h-auto bg-white rounded-[25px] shadow-md z-10">
                        <div class="w-4/5 h-auto mx-auto p-5 mt-9">
                            <div class="flex md:flex-row flex-col w-4/5 mx-auto">
                                <img class="w-2/8 md:w-2/8 pr-2 z-10 mx-auto" src="../assets/icons/assistent_on.png" />
                                <div class="w-6/8 font-bold mx-auto text-center font-OpenSans z-10 uppercase">assistent</div>
                            </div>
                            <p class="text-tiny mt-5 text-center md:text-left">
                                Unsere Assistenten unterstützen <span class="font-bold">per Sprache</span> direkt beim kochen <span class="font-bold">zu jeder Sekunde</span>. Sie untersützen wenn nur noch wenig im Kühlschrank ist, die <span class="font-bold">Zutaten aber verwertet</span> werden müssen. Unsere Assistenten unterstützen wenn ein neues Rezept erstellt oder importiert werden muss.
                            </p>
                        </div>
                    </div>
                    <!-- element -->
                    <div class="w-[278px] h-auto bg-white rounded-[25px] shadow-md z-10">
                        <div class="w-5/5 h-auto mx-auto p-5 mt-9">
                            <div class="flex md:flex-row flex-col w-4/5 mx-auto">
                                <img class="w-2/8 md:w-2/8 pr-2 z-10 mx-auto" src="../assets/icons/rezepte.png" />
                                <div class="w-6/8 font-bold mx-auto text-center font-OpenSans z-10 uppercase">meine rezepte</div>
                            </div>
                            <p class="text-tiny mt-5 text-center md:text-left">
                                Selber Rezepte nach seinen Wünschen zu verändern ist ein legitimer Wunsch. Nur wenige etablierte Anbieter änlicher Plattformen bieten diese Funktionalität. Deshalb ist es uns speziell ein Anliegen Sie bei der <span class="font-bold">genauen Weiterentwicklung und Inividualisierung</span> aller Rezepte zu unterstützen. 
                            </p>
                        </div>
                    </div>
                    <!-- element -->
                    <div class="w-[278px] h-auto bg-white rounded-[25px] shadow-md z-10">
                        <div class="w-4/5 h-auto mx-auto p-5 mt-9">
                            <div class="flex md:flex-row flex-col w-4/5 mx-auto">
                                <img class="w-2/8 md:w-2/8 pr-2 z-10 mx-auto" src="../assets/icons/kuechentisch.png" />
                                <div class="w-6/8 font-bold mx-auto text-center font-OpenSans z-10 uppercase">küchentisch</div>
                            </div>
                            <p class="text-tiny mt-5 text-center md:text-left">
                                Bringen Sie <span class="font-bold">Kollegen und Familie an den Küchentisch</span> und unterstützen Sie sich gemeinsam beim kochen, essen und geniessen.
                            </p>
                        </div>
                    </div>
                    
                </div>
            </div>
            <!-- second 
            <div class="md:w-9/12 w-full mx-auto pb-24 flex flex-col md:flex-row h-auto mt-40 md:pl-12 z-10">
                <div class="md:w-4/12 w-12/12 mx-auto mt-8 md:ml-4 z-10 flex md:flex-row flex-col">
                    <div class="z-0 w-11/12 my-auto mx-auto rounded-2xl">
                        <video
                            id="player"
                            @click="playVideoInFullScreen()"
                            poster="../assets/img/ordy_big_overview_1_poster.jpg"
                            class="rounded-2xl max-h-128" 
                            muted 
                            playsinline
                        >
                        <source
                            size="1080"
                            src="../assets/videos/ordy_big_overview_1.mp4"
                            type="video/mp4"
                            />
                        </video>
                    </div>
                    <div 
                    @click="playVideoInFullScreen()" 
                    class="transition hover:-translate-y-1 hover:scale-110 duration-300 ease-in-out w-1/12 z-10 mx-auto text-center my-auto md:-ml-[45%] lg:-ml-[55%] mb-12 mt-2"
                    >
                        <img v-if="!videoIsPlaying" src="../assets/icons/_play.png" class="max-h-20 z-50 mx-auto my-auto hover:w-[140%]" />
                    </div>
                </div>
                <div class="md:w-1/2 w-[90%] z-10 mx-auto">
                    <div class="font-bold w-full text-ordypurple-200 text-center md:text-right">ZUSAMMEN ERREICHT MAN MEHR</div>
                    <h2 class="font-YesevaOne text-[3em] h-auto font-normal leading-[6rem] w-full text-center md:text-right">Vorteile</h2>
                    <p class="text-center md:text-right md:pl-12">Verplanen der Woche so einfach gemacht. Alle wissen an welchem Tag welches Essen serviert wird. Spontane änderungen von den Tagen? Kein Problem...
                        <br /><br />Bei Ordy können Sie ihren kulinarischen Horizont erweitern und individuelle Menüs und Rezepte kreieren, teilen und weiterentwickeln. Unsere Plattform richtet sich an alle Kochbegeisterten, die Spaß daran haben neue Gerichte auszuprobieren oder ihre eigenen Rezeptideen mit anderen zu teilen.
                    </p>
                </div>
            </div>-->


        </div>

        <footerSection :isHidden="false"></footerSection>
       
    </div>
</template>
<script setup>
    import { ref, reactive, onMounted, onUnmounted, nextTick, defineAsyncComponent } from 'vue';
    //import useUser from '../../modules/useUser';
    import { useRouter } from 'vue-router';
    import { useUserStore } from '../store/userStore';
    import carouselBanner from './carouselBanner.vue'
    import footerSection from './footerSection.vue'
    
    // NEUE IMPORTS für menuCard
    import reloadIcon from '../assets/icons/reload.png';
    import peopleIcon from '../assets/icons/people.png';
    import addIcon from '../assets/icons/add.png';
    import bernerRoestiImg from '../assets/img/_bernerroesti.png'; 

    // NEUE IMPORTS für Lottie Animation
    // import { VueLottiePlayer } from "@lottiefiles/vue-lottie-player"; // Auskommentiert für dynamischen Import
    import lottieAnimationFile from '@/assets/animations/startpage_animation_menuCard.json';
    
    const userStore = useUserStore()

    // Lottie-Player dynamisch importieren - ALTE VERSION AUSKOMMENTIERT
    // const VueLottiePlayer = defineAsyncComponent(() =>
    //     import('@lottiefiles/vue-lottie-player').then(module => module.VueLottiePlayer || module.default)
    // );

    // NEUER IMPORT für vue-lottie - WIRD ERSETZT DURCH vue3-lottie
    // import LottieAnimation from 'vue-lottie';

    // KORREKTER IMPORT für vue3-lottie
    import { Vue3Lottie } from 'vue3-lottie';

    const videoIsPlaying = ref(false);

    // NEUE Refs und Logik für menuCard
    const startCardAnimations = ref(false);
    const displayedTitle = ref('');
    const targetTitle = ref('Berner Rösti mit Käse und Ei'); 
    let typingInterval = null;
    let typingCursorInterval = null;

    // NEUE Refs für Lottie (angepasst für vue3-lottie)
    const lottieAnimation = ref(null); // Zurück zum einfachen Ref für animationData
    const animationDataLoaded = ref(false);

    const scrollToSection = () => {
        const element = document.getElementById('overtheapp');
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    }

    const playVideoInFullScreen = (event) => {
        //console.log("go")
        //console.log(event)
        let player = document.getElementById("player")
        if(player){ // Sicherstellen, dass player existiert
            player.play();
            player.webkitEnterFullscreen();
            videoIsPlaying.value = true;
        }
    }

    const router = useRouter();
    const goLogs = () => {
        router.push({ name: 'logs'})
    };
    const goLogin = () => {
        router.push({ name: 'kochbuch'})
    };

    // Setup function to restore authentication state after login (cookie banner has to be deactivated)
    const backend_session_token = localStorage.getItem('backend_session_token')
    const session_token = localStorage.getItem('session_token')
    const user_id = localStorage.getItem('user_id')

    const startupfunction = async (user_id, backend_session_token, session_token) => {
        ////////////////////// SETUP ////////////////////////////////
        /*if(backend_session_token){
            await userStore.getUser(user_id, backend_session_token, session_token)
        }*/
    }

    onMounted(async() => {
        startupfunction(user_id, backend_session_token, session_token)

        // Lottie Animation laden (angepasst für vue3-lottie)
        lottieAnimation.value = lottieAnimationFile;
        animationDataLoaded.value = true;

        // NEUE Logik für menuCard Animationen
        // Animationen für die Karte starten
        setTimeout(() => {
          startCardAnimations.value = true;
        }, 100); 

        // Titel-Tipp-Effekt starten
        let charIndex = 0;
        targetTitle.value = "Berner Rösti mit Käse und Ei"; 
        displayedTitle.value = ''; 

        clearInterval(typingInterval); 
        clearInterval(typingCursorInterval);
        document.documentElement.style.removeProperty('--typing-cursor-opacity');


        typingInterval = setInterval(() => {
          if (charIndex < targetTitle.value.length) {
            displayedTitle.value += targetTitle.value.charAt(charIndex);
            charIndex++;
          } else {
            clearInterval(typingInterval);
            // Start cursor blinking after typing finishes
            let cursorVisible = true;
            typingCursorInterval = setInterval(() => {
              cursorVisible = !cursorVisible;
              document.documentElement.style.setProperty('--typing-cursor-opacity', cursorVisible ? '1' : '0');
            }, 500); // Blink speed
          }
        }, 70); // Typing speed

        // Glanz-Effekt für Buttons wird hier NICHT initialisiert, da die Buttons hier keine shiny-Klasse haben
    })
    
    // NEU: onUnmounted für Cleanup
    onUnmounted(() => {
      clearInterval(typingInterval);
      clearInterval(typingCursorInterval);
      document.documentElement.style.removeProperty('--typing-cursor-opacity');
    })

</script>
<style>
    .text-xl{
        line-height: 0.86em;
    }
    .bg-ordy{
        background: linear-gradient(152.67deg, #FFFFFF -4.31%, #EBDFFF 55.19%, #F8E5F3 117.2%, #FFFFFF 117.21%);
    }

    /* NEUE Keyframes und Styles für menuCard */
    /* --- Animationen für die Karte --- */
    @keyframes pop-background {
      0% { /* transform: scale(0.95); */ opacity: 0.8; } /* scale entfernt */
      50% { /* transform: scale(1.05); */ opacity: 1; } /* scale entfernt */
      100% { /* transform: scale(1); */ opacity: 1; } /* scale entfernt */
    }

    @keyframes jumpAnimation {
      0% { transform: translateY(10px) scale(0.9); opacity: 0; }
      70% { transform: translateY(-5px) scale(1.05); opacity: 1; }
      100% { transform: translateY(0) scale(1); opacity: 1; }
    }

    @keyframes spinFast {
      from { transform: rotate(0deg); }
      to { transform: rotate(720deg); }
    }

    @keyframes revealButtonText {
      from { width: 0; }
      to { width: 100%; }
    }

    /* Tipp-Cursor für den Titel */
    .title-element::after {
      content: '|';
      position: absolute;
      right: -2px;
      top: 0;
      bottom: 0;
      opacity: var(--typing-cursor-opacity, 0);
      animation: blink 1s step-end infinite;
    }

    @keyframes blink {
      from, to { opacity: 1; }
      50% { opacity: 0; }
    }


    /* Animations-Trigger-Klassen für die Karte */
    .card-animations-active {
      /* animation: pop-background 0.4s ease-out forwards; */ /* Auskommentiert */
    }

    .card-animations-active .footer-button-1 { animation: jumpAnimation 0.6s ease-out forwards 0.5s; opacity: 0; }
    .card-animations-active .footer-button-2 { animation: jumpAnimation 0.6s ease-out forwards 0.8s; opacity: 0; }
    .card-animations-active .footer-button-3 { animation: jumpAnimation 0.6s ease-out forwards 1.1s; opacity: 0; }

    .card-animations-active .reload-icon-img {
      animation: spinFast 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards 0.3s;
    }


    .button-typing-text {
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      width: 0;
      vertical-align: top;
    }
    .card-animations-active .footer-button-2 .button-typing-text {
      animation: revealButtonText 0.4s steps(10, end) forwards 0.9s;
    }

    /* Rahmen-Effekt für menuCard via ::before */
    .menu-card-copy {
      /* position: relative; */ /* Ist schon via Tailwind gesetzt */
      /* overflow: hidden; */ /* Ist schon via Tailwind gesetzt */
    }

    .menu-card-copy::before {
       content: "";
       position: absolute;
       z-index: 1;
       pointer-events: none;
       top: -3px;
       left: -3px;
       right: -3px;
       bottom: -3px;
       border-radius: inherit; /* Übernimmt rounded-xl vom Parent */

       background: linear-gradient(135deg,
                    transparent 0%,
                    transparent 80%,
                    rgba(220, 175, 90, 0.5) 95%,
                    #f0d9a3 100%);
     }
     /* Glanz-Effekt CSS wird hier nicht benötigt */

    /* Wrapper für Lottie und MenuCard, um sicherzustellen, dass er die Größe der MenuCard annimmt */
    /* .animation-menu-card-wrapper ist jetzt nicht mehr direkt für die Lottie-Platzierung zuständig,
       kann aber für die Größensteuerung der menu-card-copy beibehalten werden, wenn die Karte kleiner sein soll als der md:w-4/12 Container.
       Für diesen Versuch entfernen/kommentieren wir es, da menu-card-copy w-full nimmt.
    .animation-menu-card-wrapper {
        aspect-ratio: 2/3;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
        height: 500px; TEST: Explizite Höhe
        background-color: rgba(0, 255, 0, 0.2); TEST: Sichtbarer Hintergrund für den Wrapper
    }
    */

    .lottie-host-container {
      /* Dieser Container hält sowohl die Lottie-Animation als auch die Menükarte */
      min-height: 450px; /* Passe diesen Wert an die erwartete Höhe deiner Menükarte an, oder mache ihn etwas größer */
      /* background-color: rgba(0, 0, 255, 0.2); */ /* Test-Hintergrund entfernt */
    }

    .lottie-animation-container {
      /* Dieser Div ist absolut positioniert und füllt den lottie-host-container */
      /* background-color: rgba(255, 0, 0, 0.2); */ /* Test-Hintergrund entfernt */
    }

    /* Stile für den Lottie-Player, der jetzt als direkter Hintergrund dient */
    .lottie-player-instance-bg {
        /* Stellt sicher, dass die Animation den Container bedeckt, falls Aspect Ratio nicht passt */
        object-fit: cover;
        transform: scale(1.1); /* Animation leicht vergrößern, damit sie übersteht */
    }

    /* .menu-card-copy z-index ist via Tailwind (z-1) gesetzt und sorgt dafür, dass die Karte über der Lottie-Animation liegt. */

    /* Stile für den orangen und ordy-purple rotierenden, symmetrischen Schatten hinter der Menükarte */
    .menu-card-orange-shadow {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;
    }
    .menu-card-orange-shadow-fleck {
      position: absolute;
      width: 140px;
      height: 140px;
      border-radius: 50%;
      filter: blur(10px);
      opacity: 1;
      animation: glow-corner-anim 4s linear infinite;
    }
    @keyframes glow-corner-anim {
      0% {
        top: 0; left: 0; right: auto; bottom: auto;
        background: radial-gradient(circle at 0% 0%, rgba(255,170,0,0.7) 0%, rgba(255,170,0,0.5) 50%, rgba(255,170,0,0.01) 80%);
        opacity: 1;
      }
      24% {
        top: 0; left: 0; right: auto; bottom: auto;
        background: radial-gradient(circle at 0% 0%, rgba(255,170,0,0.7) 0%, rgba(255,170,0,0.5) 50%, rgba(255,170,0,0.01) 80%);
        opacity: 1;
      }
      25% {
        top: 0; left: auto; right: 0; bottom: auto;
        background: radial-gradient(circle at 100% 0%, rgba(255,170,0,0.01) 0%, rgba(255,170,0,0.01) 100%);
        opacity: 0;
      }
      49% {
        top: 0; left: auto; right: 0; bottom: auto;
        background: radial-gradient(circle at 100% 0%, rgba(255,170,0,0.01) 0%, rgba(255,170,0,0.01) 100%);
        opacity: 0;
      }
      50% {
        top: auto; left: auto; right: 0; bottom: 0;
        background: radial-gradient(circle at 100% 100%, rgba(120,60,180,0.7) 0%, rgba(120,60,180,0.5) 50%, rgba(120,60,180,0.01) 80%);
        opacity: 1;
      }
      74% {
        top: auto; left: auto; right: 0; bottom: 0;
        background: radial-gradient(circle at 100% 100%, rgba(120,60,180,0.7) 0%, rgba(120,60,180,0.5) 50%, rgba(120,60,180,0.01) 80%);
        opacity: 1;
      }
      75% {
        top: auto; left: 0; right: auto; bottom: 0;
        background: radial-gradient(circle at 0% 100%, rgba(120,60,180,0.01) 0%, rgba(120,60,180,0.01) 100%);
        opacity: 0;
      }
      99% {
        top: auto; left: 0; right: auto; bottom: 0;
        background: radial-gradient(circle at 0% 100%, rgba(120,60,180,0.01) 0%, rgba(120,60,180,0.01) 100%);
        opacity: 0;
      }
      100% {
        top: 0; left: 0; right: auto; bottom: auto;
        background: radial-gradient(circle at 0% 0%, rgba(255,170,0,0.7) 0%, rgba(255,170,0,0.5) 50%, rgba(255,170,0,0.01) 80%);
        opacity: 1;
      }
    }

</style>